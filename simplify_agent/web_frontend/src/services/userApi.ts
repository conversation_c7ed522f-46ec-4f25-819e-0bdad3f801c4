// 用户视角API服务
import { request } from './api';
import type {
  TestExecution,
  TestPlan,
  ToolExecution,
  ComprehensiveEvaluation
} from '@/types/models';
import type {
  PaginatedResponse,
  FilterParams
} from '@/types/api';

export const userApi = {
  // 获取执行记录列表
  getExecutions: (params: FilterParams): Promise<PaginatedResponse<TestExecution>> => {
    return request.get('/user/executions', params);
  },
  
  // 获取测试计划列表
  getPlans: (params: FilterParams): Promise<PaginatedResponse<TestPlan>> => {
    return request.get('/user/plans', params);
  },
  
  // 获取执行记录详情
  getExecutionDetail: (executionId: string): Promise<{
    execution: TestExecution;
    plan: TestPlan;
    tools: ToolExecution[];
    evaluation: ComprehensiveEvaluation;
  }> => {
    return request.get(`/user/executions/${executionId}`);
  },
  
  // 获取测试计划详情
  getPlanDetail: (planId: string): Promise<{
    plan_id: string;
    original_request: string;
    platform: string;
    total_steps: number;
    plan_summary: string;
    structured_plan: any;
    generation_metadata?: any;
    agent_instructions?: string;
    created_at: string;
    updated_at?: string;
    executions: TestExecution[];
    execution_count: number;
  }> => {
    return request.get(`/user/plans/${planId}`);
  },
  
  // 获取工具执行列表
  getToolExecutions: (executionId: string): Promise<ToolExecution[]> => {
    return request.get(`/user/executions/${executionId}/tools`);
  },
  
  // 获取综合评价
  getComprehensiveEvaluation: (executionId: string): Promise<ComprehensiveEvaluation> => {
    return request.get(`/user/executions/${executionId}/evaluation`);
  },
  
  // 删除执行记录
  deleteExecution: (executionId: string): Promise<void> => {
    return request.delete(`/user/executions/${executionId}`);
  },
  
  // 重新执行测试
  rerunExecution: (executionId: string): Promise<{ new_execution_id: string }> => {
    return request.post(`/user/executions/${executionId}/rerun`);
  },
  
  // 导出执行记录
  exportExecution: (executionId: string): Promise<{ download_url: string }> => {
    return request.post(`/user/executions/${executionId}/export`);
  },
  
  // 健康检查
  healthCheck: (): Promise<{ status: string; service: string; version: string }> => {
    return request.get('/user/health');
  },
};