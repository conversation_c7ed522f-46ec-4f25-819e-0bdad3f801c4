// 测试执行详情页面
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import AppLayout from '@/components/common/AppLayout';
import {
  Card,
  Typography,
  Tag,
  Button,
  Space,
  Descriptions,
  Timeline,
  Alert,
  Spin,
  Tabs,
  Progress,
  BackTop
} from 'antd';
import {
  ArrowLeftOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  PlayCircleOutlined,
  DownloadOutlined,
  FileTextOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { userApi } from '@/services/userApi';
import { 
  ROUTES, 
  EXECUTION_STATUS_LABELS, 
  EXECUTION_STATUS_COLORS,
  TOOL_STATUS_LABELS,
  TOOL_STATUS_COLORS
} from '@/constants';
import { formatTime, formatDuration, getTimeAgo } from '@/utils';
import type { 
  TestExecution, 
  TestPlan, 
  ToolExecution, 
  ComprehensiveEvaluation 
} from '@/types/models';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

const UserExecutionDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(true);
  const [execution, setExecution] = useState<TestExecution | null>(null);
  const [plan, setPlan] = useState<TestPlan | null>(null);
  const [tools, setTools] = useState<ToolExecution[]>([]);
  const [evaluation, setEvaluation] = useState<ComprehensiveEvaluation | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 加载执行详情数据
  const loadExecutionDetail = async () => {
    if (!id) return;

    try {
      setLoading(true);
      setError(null);

      const data = await userApi.getExecutionDetail(id);
      setExecution(data.execution);
      setPlan(data.plan);
      setTools(data.tools);
      setEvaluation(data.evaluation);

    } catch (err) {
      console.error('Load execution detail failed:', err);
      setError('加载执行详情失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadExecutionDetail();
  }, [id]);

  // 返回列表页
  const handleBackToList = () => {
    navigate(ROUTES.USER_EXECUTIONS);
  };

  // 重新执行
  const handleRerun = async () => {
    if (!execution) return;
    
    try {
      const result = await userApi.rerunExecution(execution.execution_id);
      navigate(ROUTES.USER_EXECUTION_DETAIL.replace(':id', result.new_execution_id));
    } catch (error) {
      console.error('Rerun execution failed:', error);
    }
  };

  // 导出数据
  const handleExport = async () => {
    if (!execution) return;
    
    try {
      const result = await userApi.exportExecution(execution.execution_id);
      window.open(result.download_url, '_blank');
    } catch (error) {
      console.error('Export execution failed:', error);
    }
  };

  if (error) {
    return (
      <AppLayout mode="user">
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Space>
              <Button size="small" onClick={loadExecutionDetail}>
                重新加载
              </Button>
              <Button size="small" onClick={handleBackToList}>
                返回列表
              </Button>
            </Space>
          }
        />
      </AppLayout>
    );
  }

  if (loading || !execution) {
    return (
      <AppLayout mode="user">
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>正在加载执行详情...</div>
        </div>
      </AppLayout>
    );
  }

  // 计算执行进度
  const getExecutionProgress = () => {
    const totalSteps = plan?.total_steps || 0;
    const completedSteps = tools.length;
    return totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;
  };

  return (
    <AppLayout mode="user">
      <div>
        {/* 页面标题栏 */}
        <div style={{ marginBottom: 24 }}>
          <Space>
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={handleBackToList}
            >
              返回列表
            </Button>
            <Title level={3} style={{ margin: 0 }}>
              执行详情
            </Title>
            <Tag color={EXECUTION_STATUS_COLORS[execution.execution_status]}>
              {EXECUTION_STATUS_LABELS[execution.execution_status]}
            </Tag>
          </Space>
          
          <div style={{ marginTop: 16 }}>
            <Space>
              <Button 
                type="primary" 
                icon={<PlayCircleOutlined />}
                onClick={handleRerun}
              >
                重新执行
              </Button>
              <Button 
                icon={<DownloadOutlined />}
                onClick={handleExport}
              >
                导出数据
              </Button>
            </Space>
          </div>
        </div>

        {/* 基础信息卡片 */}
        <Card title="基础信息" style={{ marginBottom: 24 }}>
          <Descriptions column={{ xs: 1, sm: 2, md: 2, lg: 3 }}>
            <Descriptions.Item label="执行ID">
              <Text code>{execution.execution_id}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="执行状态">
              <Tag color={EXECUTION_STATUS_COLORS[execution.execution_status]}>
                {EXECUTION_STATUS_LABELS[execution.execution_status]}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="总成功状态">
              <Tag color={execution.overall_success === 'success' ? 'green' : 'red'}>
                {execution.overall_success === 'success' ? '成功' : '失败'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="执行时长">
              {formatDuration(execution.total_duration)}
            </Descriptions.Item>
            <Descriptions.Item label="执行轮数">
              {execution.total_rounds}
            </Descriptions.Item>
            <Descriptions.Item label="执行进度">
              <Progress 
                percent={getExecutionProgress()} 
                size="small" 
                style={{ width: 100 }}
              />
            </Descriptions.Item>
            <Descriptions.Item label="开始时间">
              {formatTime(execution.start_time)}
            </Descriptions.Item>
            <Descriptions.Item label="结束时间">
              {formatTime(execution.end_time)}
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {getTimeAgo(execution.created_at)}
            </Descriptions.Item>
          </Descriptions>
          
          <div style={{ marginTop: 16 }}>
            <Text strong>原始请求：</Text>
            <div style={{ marginTop: 8, padding: 12, background: '#f5f5f5', borderRadius: 4 }}>
              {execution.original_request}
            </div>
          </div>
        </Card>

        {/* Tab页 */}
        <Card>
          <Tabs defaultActiveKey="plan" size="large">
            {/* 测试计划 */}
            <TabPane
              tab={
                <span>
                  <FileTextOutlined />
                  测试计划
                </span>
              }
              key="plan"
            >
              {plan ? (
                <div>
                  <Descriptions column={{ xs: 1, sm: 2 }} style={{ marginBottom: 24 }}>
                    <Descriptions.Item label="计划ID">
                      <Text code>{plan.plan_id}</Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="平台">
                      <Tag>{plan.platform}</Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="总步数">
                      {plan.total_steps}
                    </Descriptions.Item>
                    <Descriptions.Item label="创建时间">
                      {getTimeAgo(plan.created_at)}
                    </Descriptions.Item>
                  </Descriptions>

                  <div style={{ marginBottom: 24 }}>
                    <Text strong>计划摘要：</Text>
                    <Paragraph style={{ marginTop: 8 }}>
                      {plan.plan_summary}
                    </Paragraph>
                  </div>

                  {plan.structured_plan?.steps && (
                    <div>
                      <Text strong>执行步骤：</Text>
                      <Timeline style={{ marginTop: 16 }}>
                        {plan.structured_plan.steps.map((step, index) => (
                          <Timeline.Item 
                            key={index}
                            dot={
                              <div style={{
                                width: 20,
                                height: 20,
                                borderRadius: '50%',
                                backgroundColor: '#1890ff',
                                color: 'white',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '12px'
                              }}>
                                {step.step}
                              </div>
                            }
                          >
                            <div>
                              <Text strong>步骤 {step.step}：</Text>
                              <div style={{ marginTop: 4 }}>{step.action}</div>
                              {step.expected_result && (
                                <div style={{ marginTop: 4, color: '#666' }}>
                                  <Text type="secondary">预期结果：{step.expected_result}</Text>
                                </div>
                              )}
                            </div>
                          </Timeline.Item>
                        ))}
                      </Timeline>
                    </div>
                  )}
                </div>
              ) : (
                <Alert message="测试计划信息不可用" type="warning" />
              )}
            </TabPane>

            {/* 执行流程 */}
            <TabPane
              tab={
                <span>
                  <PlayCircleOutlined />
                  执行流程
                </span>
              }
              key="flow"
            >
              {tools.length > 0 ? (
                <Timeline>
                  {tools.map((tool, index) => (
                    <Timeline.Item
                      key={index}
                      dot={
                        tool.tool_status === 'success' ? (
                          <CheckCircleOutlined style={{ color: '#52c41a' }} />
                        ) : tool.tool_status === 'timeout' ? (
                          <ClockCircleOutlined style={{ color: '#faad14' }} />
                        ) : (
                          <ExclamationCircleOutlined style={{ color: '#f5222d' }} />
                        )
                      }
                    >
                      <Card size="small" style={{ marginBottom: 16 }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                          <div style={{ flex: 1 }}>
                            <Space>
                              <Text strong>轮次 {tool.round_number}</Text>
                              <Tag>{tool.tool_name}</Tag>
                              <Tag color={TOOL_STATUS_COLORS[tool.tool_status]}>
                                {TOOL_STATUS_LABELS[tool.tool_status]}
                              </Tag>
                            </Space>
                            
                            <div style={{ marginTop: 8 }}>
                              <Text type="secondary">
                                执行时长: {formatDuration(tool.execution_time)}
                              </Text>
                            </div>

                            {tool.tool_parameters && (
                              <div style={{ marginTop: 8 }}>
                                <Text strong>参数：</Text>
                                <pre style={{ 
                                  background: '#f5f5f5', 
                                  padding: 8, 
                                  borderRadius: 4,
                                  fontSize: '12px',
                                  marginTop: 4
                                }}>
                                  {JSON.stringify(tool.tool_parameters, null, 2)}
                                </pre>
                              </div>
                            )}

                            <div style={{ marginTop: 12 }}>
                              <Text strong>结果摘要：</Text>
                              <div style={{ marginTop: 4 }}>
                                {tool.result_summary}
                              </div>
                            </div>
                          </div>
                        </div>
                      </Card>
                    </Timeline.Item>
                  ))}
                </Timeline>
              ) : (
                <Alert message="暂无工具执行记录" type="info" />
              )}
            </TabPane>

            {/* 综合评价 */}
            <TabPane
              tab={
                <span>
                  <EyeOutlined />
                  综合评价
                </span>
              }
              key="evaluation"
            >
              {evaluation ? (
                <div>
                  <Descriptions column={{ xs: 1, sm: 2 }} style={{ marginBottom: 24 }}>
                    <Descriptions.Item label="评估模型">
                      <Text code>{evaluation.analysis_model}</Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="最终状态">
                      <Tag color={evaluation.final_success_status === 'success' ? 'green' : 'red'}>
                        {evaluation.final_success_status === 'success' ? '成功' : '失败'}
                      </Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="成功分数">
                      <Progress 
                        type="line" 
                        percent={Math.round(evaluation.overall_success_score * 100)}
                        format={percent => `${percent}%`}
                        style={{ width: 200 }}
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="置信度">
                      <Progress 
                        type="line" 
                        percent={Math.round(evaluation.confidence_score * 100)}
                        strokeColor="#52c41a"
                        format={percent => `${percent}%`}
                        style={{ width: 200 }}
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="评估轮次">
                      {evaluation.evaluation_round}
                    </Descriptions.Item>
                    <Descriptions.Item label="评估时间">
                      {getTimeAgo(evaluation.created_at)}
                    </Descriptions.Item>
                  </Descriptions>

                  <div style={{ marginBottom: 24 }}>
                    <Text strong>评估摘要：</Text>
                    <div style={{ 
                      marginTop: 8, 
                      padding: 16, 
                      background: '#f9f9f9', 
                      borderRadius: 4,
                      border: '1px solid #d9d9d9'
                    }}>
                      {evaluation.evaluation_summary}
                    </div>
                  </div>

                  <div>
                    <Text strong>详细分析：</Text>
                    <div style={{ 
                      marginTop: 8, 
                      padding: 16, 
                      background: '#f9f9f9', 
                      borderRadius: 4,
                      border: '1px solid #d9d9d9',
                      whiteSpace: 'pre-wrap'
                    }}>
                      {evaluation.comprehensive_analysis}
                    </div>
                  </div>
                </div>
              ) : (
                <Alert message="暂无综合评价信息" type="info" />
              )}
            </TabPane>
          </Tabs>
        </Card>

        <BackTop />
      </div>
    </AppLayout>
  );
};

export default UserExecutionDetail;