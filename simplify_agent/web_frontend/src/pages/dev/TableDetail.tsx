// 表数据详情页面
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import AppLayout from '@/components/common/AppLayout';
import {
  Typography,
  Card,
  Table,
  Button,
  Space,
  Input,
  Modal,
  Form,
  message,
  Popconfirm,
  Alert,
  Spin,
  Tag,
  Row,
  Col,
  Breadcrumb
} from 'antd';
import {
  ArrowLeftOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  TableOutlined,
  SaveOutlined,
  CloseOutlined
} from '@ant-design/icons';
import { devApi } from '@/services/devApi';
import { ROUTES } from '@/constants';
import type { TableRecord, DatabaseTable } from '@/types/models';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;
const { Search } = Input;

interface TableSchema {
  table_name: string;
  columns: Array<{
    name: string;
    type: string;
    not_null: boolean;
    default_value: any;
    primary_key: boolean;
  }>;
  indexes: any[];
  foreign_keys: any[];
}

const DevTableDetail: React.FC = () => {
  const { tableName } = useParams<{ tableName: string }>();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(true);
  const [records, setRecords] = useState<TableRecord[]>([]);
  const [schema, setSchema] = useState<TableSchema | null>(null);
  const [tableInfo, setTableInfo] = useState<DatabaseTable | null>(null);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [searchText, setSearchText] = useState('');
  const [error, setError] = useState<string | null>(null);
  
  // 模态框状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'create' | 'edit'>('create');
  const [editingRecord, setEditingRecord] = useState<TableRecord | null>(null);
  const [form] = Form.useForm();

  // 加载表结构
  const loadSchema = async () => {
    if (!tableName) return;
    
    try {
      const schemaData = await devApi.getTableSchema(tableName);
      setSchema(schemaData);
    } catch (error) {
      console.error('Load schema failed:', error);
    }
  };

  // 加载表数据
  const loadTableData = async (page = currentPage, size = pageSize, search = searchText) => {
    if (!tableName) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const params = {
        page,
        page_size: size,
        search: search || undefined
      };
      
      const result = await devApi.getTableData(tableName, params);
      setRecords(result.items);
      setTotal(result.pagination.total_items);
      setCurrentPage(page);
    } catch (error: any) {
      console.error('Load table data failed:', error);
      setError('加载表数据失败：' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 加载表信息
  const loadTableInfo = async () => {
    if (!tableName) return;
    
    try {
      const result = await devApi.getTableList();
      const table = result.tables.find(t => t.table_name === tableName);
      setTableInfo(table || null);
    } catch (error) {
      console.error('Load table info failed:', error);
    }
  };

  useEffect(() => {
    if (tableName) {
      Promise.all([
        loadSchema(),
        loadTableData(),
        loadTableInfo()
      ]);
    }
  }, [tableName]);

  // 创建记录
  const handleCreate = async (values: any) => {
    if (!tableName) return;
    
    try {
      await devApi.createRecord(tableName, values);
      message.success('创建记录成功');
      setModalVisible(false);
      form.resetFields();
      loadTableData();
    } catch (error: any) {
      message.error('创建失败：' + (error.response?.data?.message || error.message));
    }
  };

  // 更新记录
  const handleUpdate = async (values: any) => {
    if (!tableName || !editingRecord) return;
    
    try {
      // 假设第一个主键字段作为记录ID
      const primaryKey = schema?.columns.find(col => col.primary_key);
      if (!primaryKey) {
        message.error('未找到主键，无法更新记录');
        return;
      }
      
      const recordId = editingRecord[primaryKey.name];
      await devApi.updateRecord(tableName, recordId, values);
      message.success('更新记录成功');
      setModalVisible(false);
      form.resetFields();
      setEditingRecord(null);
      loadTableData();
    } catch (error: any) {
      message.error('更新失败：' + (error.response?.data?.message || error.message));
    }
  };

  // 删除记录
  const handleDelete = async (record: TableRecord) => {
    if (!tableName) return;
    
    try {
      // 假设第一个主键字段作为记录ID
      const primaryKey = schema?.columns.find(col => col.primary_key);
      if (!primaryKey) {
        message.error('未找到主键，无法删除记录');
        return;
      }
      
      const recordId = record[primaryKey.name];
      await devApi.deleteRecord(tableName, recordId);
      message.success('删除记录成功');
      loadTableData();
    } catch (error: any) {
      message.error('删除失败：' + (error.response?.data?.message || error.message));
    }
  };

  // 打开编辑模态框
  const openEditModal = (record: TableRecord) => {
    setModalType('edit');
    setEditingRecord(record);
    form.setFieldsValue(record);
    setModalVisible(true);
  };

  // 打开创建模态框
  const openCreateModal = () => {
    setModalType('create');
    setEditingRecord(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 生成表格列
  const generateColumns = (): ColumnsType<TableRecord> => {
    if (!schema) return [];
    
    const columns: ColumnsType<TableRecord> = schema.columns.map(col => ({
      title: (
        <span>
          {col.name}
          {col.primary_key && <Tag color="red" style={{ marginLeft: 4, fontSize: '11px' }}>PK</Tag>}
          {col.not_null && <Tag color="orange" style={{ marginLeft: 4, fontSize: '11px' }}>NOT NULL</Tag>}
        </span>
      ),
      dataIndex: col.name,
      key: col.name,
      ellipsis: true,
      render: (value: any) => {
        if (value === null || value === undefined) {
          return <Text type="secondary">NULL</Text>;
        }
        if (typeof value === 'object') {
          return <Text code>{JSON.stringify(value)}</Text>;
        }
        return String(value);
      }
    }));

    // 添加操作列
    columns.push({
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button 
            size="small" 
            icon={<EditOutlined />}
            onClick={() => openEditModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              size="small" 
              danger 
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    });

    return columns;
  };

  if (!tableName) {
    return (
      <AppLayout mode="dev">
        <Alert message="表名参数缺失" type="error" />
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout mode="dev">
        <Alert
          message="数据加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Space>
              <Button size="small" onClick={() => loadTableData()}>
                重新加载
              </Button>
              <Button size="small" onClick={() => navigate(ROUTES.DEV_TABLES)}>
                返回表列表
              </Button>
            </Space>
          }
        />
      </AppLayout>
    );
  }

  return (
    <AppLayout mode="dev">
      <div>
        {/* 面包屑导航 */}
        <Breadcrumb style={{ marginBottom: 16 }}>
          <Breadcrumb.Item>
            <Button 
              type="link" 
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate(ROUTES.DEV_TABLES)}
              style={{ padding: 0 }}
            >
              数据库表
            </Button>
          </Breadcrumb.Item>
          <Breadcrumb.Item>{tableName}</Breadcrumb.Item>
        </Breadcrumb>

        {/* 页面标题 */}
        <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3}>
            <TableOutlined /> {tableName} 表管理
          </Title>
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={() => loadTableData()}
              loading={loading}
            >
              刷新
            </Button>
            <Button 
              type="primary"
              icon={<PlusOutlined />}
              onClick={openCreateModal}
            >
              新增记录
            </Button>
          </Space>
        </div>

        {/* 表信息概览 */}
        {tableInfo && (
          <Card size="small" style={{ marginBottom: 16 }}>
            <Row gutter={[16, 8]}>
              <Col span={6}>
                <Text strong>记录总数:</Text> {tableInfo.record_count.toLocaleString()}
              </Col>
              <Col span={6}>
                <Text strong>字段数:</Text> {tableInfo.column_count}
              </Col>
              <Col span={6}>
                <Text strong>主键:</Text> {schema?.columns.filter(col => col.primary_key).map(col => col.name).join(', ') || '无'}
              </Col>
              <Col span={6}>
                <Text strong>索引数:</Text> {schema?.indexes?.length || 0}
              </Col>
            </Row>
          </Card>
        )}

        {/* 搜索栏 */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]} align="middle">
            <Col flex="auto">
              <Search
                placeholder="搜索记录（支持模糊匹配所有字段）"
                allowClear
                enterButton
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                onSearch={(value) => loadTableData(1, pageSize, value)}
              />
            </Col>
            <Col>
              显示 {records.length} / {total} 条记录
            </Col>
          </Row>
        </Card>

        {/* 数据表格 */}
        <Card>
          <Spin spinning={loading}>
            <Table
              columns={generateColumns()}
              dataSource={records}
              rowKey={(record) => {
                const primaryKey = schema?.columns.find(col => col.primary_key);
                return primaryKey ? String(record[primaryKey.name]) : Math.random().toString();
              }}
              pagination={{
                current: currentPage,
                pageSize: pageSize,
                total: total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `显示第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
                onChange: (page, size) => {
                  setCurrentPage(page);
                  setPageSize(size || pageSize);
                  loadTableData(page, size || pageSize);
                }
              }}
              size="small"
              scroll={{ x: true }}
            />
          </Spin>
        </Card>

        {/* 创建/编辑模态框 */}
        <Modal
          title={modalType === 'create' ? `新增 ${tableName} 记录` : `编辑 ${tableName} 记录`}
          open={modalVisible}
          onCancel={() => {
            setModalVisible(false);
            form.resetFields();
            setEditingRecord(null);
          }}
          footer={[
            <Button 
              key="cancel" 
              icon={<CloseOutlined />}
              onClick={() => {
                setModalVisible(false);
                form.resetFields();
                setEditingRecord(null);
              }}
            >
              取消
            </Button>,
            <Button
              key="submit"
              type="primary"
              icon={<SaveOutlined />}
              onClick={() => form.submit()}
            >
              {modalType === 'create' ? '创建' : '更新'}
            </Button>
          ]}
          width={600}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={modalType === 'create' ? handleCreate : handleUpdate}
          >
            {schema?.columns.map(col => (
              <Form.Item
                key={col.name}
                name={col.name}
                label={
                  <span>
                    {col.name}
                    {col.primary_key && <Tag color="red" style={{ marginLeft: 4, fontSize: '11px' }}>主键</Tag>}
                    {col.not_null && <Tag color="orange" style={{ marginLeft: 4, fontSize: '11px' }}>必填</Tag>}
                    <Text type="secondary" style={{ marginLeft: 8 }}>({col.type})</Text>
                  </span>
                }
                rules={[
                  { 
                    required: col.not_null && modalType === 'create', 
                    message: `${col.name} 不能为空` 
                  }
                ]}
              >
                <Input 
                  placeholder={col.default_value ? `默认值: ${col.default_value}` : `请输入 ${col.name}`}
                  disabled={col.primary_key && modalType === 'edit'}
                />
              </Form.Item>
            ))}
          </Form>
        </Modal>
      </div>
    </AppLayout>
  );
};

export default DevTableDetail;