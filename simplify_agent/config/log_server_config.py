"""
SimplifyAgent 日志服务器配置管理模块

提供日志监控服务的集中化配置管理，包括文件路径、数据库连接、
处理策略等各项配置的统一管理和访问接口。

作者: SimplifyAgent Development Team
创建时间: 2024-09-04
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional
import json
import logging


class LogServerConfig:
    """日志服务器配置管理器"""
    
    # 默认配置
    DEFAULT_CONFIG = {
        'server': {
            'name': 'SimplifyAgent Log Server',
            'version': '1.0.0',
            'debug': False,
            'max_workers': 3,
            'health_check_interval': 30  # 秒
        },
        
        'watch_paths': {
            'json_plan': 'log/json_plan',                    # 修正为实际的json_plan路径
            'agent_execute': 'log/agent_execute_log',        # 修正为实际的agent_execute_log路径
            'judge_report': 'log/judge_report'               # 实际的judge_report路径
        },
        
        'file_limits': {
            'json_plan_max': 20,
            'agent_execute_max': 20,
            'judge_report_max': 20,
            'check_interval': 300  # 秒，文件清理检查间隔
        },
        
        'processing': {
            'batch_size': 10,
            'retry_attempts': 3,
            'retry_delay': 5,  # 秒
            'file_check_delay': 2,  # 文件稳定性检查延迟
            'max_file_size': 50 * 1024 * 1024  # 50MB 最大文件大小
        },
        
        'database': {
            'path': 'data/test_database.db',  # 使用主数据目录
            'connection_pool_size': 5,
            'timeout': 30,
            'retry_attempts': 3
        },
        
        'logging': {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file_path': 'log/log_server/log_server.log',  # 新的统一日志路径
            'max_bytes': 10 * 1024 * 1024,  # 10MB
            'backup_count': 5,
            'console_output': True
        },
        
        'monitoring': {
            'status_file': 'log/log_server/.log_server_status',
            'pid_file': 'log/log_server/.log_server.pid',
            'metrics_interval': 60,  # 秒，性能指标收集间隔
            'enable_metrics': True
        }
    }
    
    def __init__(self, config_file: Optional[str] = None, base_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 自定义配置文件路径
            base_path: 项目基础路径，用于相对路径解析
        """
        self.base_path = Path(base_path) if base_path else Path(__file__).parent.parent
        self.config_file = config_file
        self.config = self._load_config()
        self._resolve_paths()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        config = self.DEFAULT_CONFIG.copy()
        
        if self.config_file and os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                config = self._merge_config(config, user_config)
            except Exception as e:
                logging.warning(f"Failed to load config file {self.config_file}: {e}")
                logging.info("Using default configuration")
        
        return config
    
    def _merge_config(self, default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        """递归合并配置字典"""
        for key, value in user.items():
            if key in default and isinstance(default[key], dict) and isinstance(value, dict):
                default[key] = self._merge_config(default[key], value)
            else:
                default[key] = value
        return default
    
    def _resolve_paths(self):
        """解析相对路径为绝对路径"""
        path_keys = [
            ('watch_paths', ['json_plan', 'agent_execute', 'judge_report']),
            ('database', ['path']),
            ('logging', ['file_path']),
            ('monitoring', ['status_file', 'pid_file'])
        ]
        
        for section, keys in path_keys:
            for key in keys:
                if key in self.config[section]:
                    path_value = self.config[section][key]
                    if not os.path.isabs(path_value):
                        self.config[section][key] = str(self.base_path / path_value)
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值，支持点号分隔的路径
        
        Args:
            key_path: 配置键路径，如 'server.debug' 或 'watch_paths.json_plan'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any):
        """设置配置值"""
        keys = key_path.split('.')
        config = self.config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
    
    def get_watch_paths(self) -> Dict[str, str]:
        """获取监控路径配置"""
        return self.config['watch_paths'].copy()
    
    def get_file_limits(self) -> Dict[str, int]:
        """获取文件数量限制配置"""
        return self.config['file_limits'].copy()
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self.config['database'].copy()
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.config['logging'].copy()
    
    def get_processing_config(self) -> Dict[str, Any]:
        """获取处理策略配置"""
        return self.config['processing'].copy()
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """获取监控配置"""
        return self.config['monitoring'].copy()
    
    def ensure_directories(self):
        """确保所有必要的目录存在"""
        directories = []
        
        # 监控目录
        for path in self.config['watch_paths'].values():
            directories.append(path)
        
        # 日志目录
        log_file = self.config['logging']['file_path']
        directories.append(os.path.dirname(log_file))
        
        # 数据库目录
        db_path = self.config['database']['path']
        directories.append(os.path.dirname(db_path))
        
        # 状态文件目录
        for key in ['status_file', 'pid_file']:
            status_path = self.config['monitoring'][key]
            directories.append(os.path.dirname(status_path))
        
        for directory in directories:
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
    
    def save_config(self, file_path: Optional[str] = None):
        """保存当前配置到文件"""
        save_path = file_path or self.config_file
        if not save_path:
            raise ValueError("No config file path specified")
        
        # 创建相对路径版本用于保存
        save_config = self._convert_to_relative_paths()
        
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(save_config, f, indent=2, ensure_ascii=False)
    
    def _convert_to_relative_paths(self) -> Dict[str, Any]:
        """将绝对路径转换为相对路径用于配置文件保存"""
        config = json.loads(json.dumps(self.config))  # Deep copy
        
        path_keys = [
            ('watch_paths', ['json_plan', 'agent_execute', 'judge_report']),
            ('database', ['path']),
            ('logging', ['file_path']),
            ('monitoring', ['status_file', 'pid_file'])
        ]
        
        for section, keys in path_keys:
            for key in keys:
                if key in config[section]:
                    abs_path = Path(config[section][key])
                    try:
                        rel_path = abs_path.relative_to(self.base_path)
                        config[section][key] = str(rel_path)
                    except ValueError:
                        # 如果无法转换为相对路径，保持绝对路径
                        pass
        
        return config
    
    def validate_config(self) -> tuple[bool, list[str]]:
        """
        验证配置的有效性
        
        Returns:
            (is_valid, error_messages)
        """
        errors = []
        
        # 检查必要的路径
        for name, path in self.config['watch_paths'].items():
            if not os.path.exists(path):
                errors.append(f"Watch path {name} does not exist: {path}")
        
        # 检查数据库路径
        db_path = self.config['database']['path']
        db_dir = os.path.dirname(db_path)
        if not os.path.exists(db_dir):
            errors.append(f"Database directory does not exist: {db_dir}")
        
        # 检查文件限制值
        for key, value in self.config['file_limits'].items():
            if key.endswith('_max') and (not isinstance(value, int) or value <= 0):
                errors.append(f"Invalid file limit {key}: must be positive integer")
        
        # 检查处理配置
        processing = self.config['processing']
        if processing['batch_size'] <= 0:
            errors.append("Processing batch_size must be positive")
        if processing['retry_attempts'] < 0:
            errors.append("Processing retry_attempts must be non-negative")
        
        return len(errors) == 0, errors
    
    def __str__(self) -> str:
        """返回配置的字符串表示"""
        return f"LogServerConfig(base_path='{self.base_path}', config_file='{self.config_file}')"
    
    def __repr__(self) -> str:
        return self.__str__()


# 全局配置实例
_config_instance: Optional[LogServerConfig] = None


def get_config(config_file: Optional[str] = None, base_path: Optional[str] = None) -> LogServerConfig:
    """
    获取全局配置实例（单例模式）
    
    Args:
        config_file: 配置文件路径（仅首次调用时有效）
        base_path: 基础路径（仅首次调用时有效）
        
    Returns:
        配置管理器实例
    """
    global _config_instance
    
    if _config_instance is None:
        _config_instance = LogServerConfig(config_file, base_path)
    
    return _config_instance


def reset_config():
    """重置全局配置实例（主要用于测试）"""
    global _config_instance
    _config_instance = None


if __name__ == '__main__':
    # 配置模块测试
    config = get_config()
    
    print("=== SimplifyAgent 日志服务器配置信息 ===")
    print(f"服务名称: {config.get('server.name')}")
    print(f"版本: {config.get('server.version')}")
    print(f"监控路径: {config.get_watch_paths()}")
    print(f"文件限制: {config.get_file_limits()}")
    print(f"数据库路径: {config.get('database.path')}")
    
    # 验证配置
    is_valid, errors = config.validate_config()
    print(f"\n配置验证结果: {'有效' if is_valid else '无效'}")
    if errors:
        for error in errors:
            print(f"  - {error}")
    
    # 确保目录存在
    config.ensure_directories()
    print("\n目录检查完成")